<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    default-compare-id="mttr_minutes"
    :machines="machines"
    @export-data="handleExportData"
    chart-height="222px"
    ref="userTable"
  />
</template>

<script setup lang="tsx">
import { onMounted, ref, onUnmounted } from "vue";
import moment from "moment";
import { alramAnalysisApi } from "@/api";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emits = defineEmits(["dataReady", "exportData"]);
// const props = defineProps({
//   machines: {
//     type: Array as PropType<Array<{ id: string; name: string }>>,
//     default: () => []
//   }
// });
// const machines = ref<any[]>([]);
const handleExportData = (csvContent: string) => {
  emits("exportData", {
    data: csvContent
  });
};

// 颜色配置常量
const COLORS = {
  mttr_minutes: "#00bfff",
  mtbf_minutes: "#ff4d4f",
  total_fault_time_minutes: "#fac858",
  fault_count: "#87d068",
  failure_rate: "#ffa940",
  font: "#666",
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("common.compareList.mttr_minutes"), //"MTTR",
    value: "mttr_minutes"
  },
  {
    label: t("common.compareList.mtbf_minutes"), //"MTBF",
    value: "mtbf_minutes"
  },
  {
    label: t("common.compareList.total_fault_time_minutes"), //"总故障时间",
    value: "total_fault_time_minutes"
  },
  {
    label: t("common.compareList.fault_count"), //"故障次数",
    value: "fault_count"
  },
  {
    label: t("common.compareList.failure_rate"), //"故障率",
    value: "failure_rate"
  }
];

// 图表配置
const chartOptions = ref({
  title: {
    subtext: t("common.compareList.faultStatistics"), //"故障统计信息",
    left: "center",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    top: -12
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const mttr_minutes = params.find((p: any) => p.seriesName === t("common.compareList.mttr_minutes"));
      const mtbf_minutes = params.find((p: any) => p.seriesName === t("common.compareList.mtbf_minutes"));
      const total_fault_time_minutes = params.find((p: any) => p.seriesName === t("common.compareList.total_fault_time_minutes"));
      const fault_count = params.find((p: any) => p.seriesName === t("common.compareList.fault_count"));
      const failure_rate = params.find((p: any) => p.seriesName === t("common.compareList.failure_rate"));
      return `
          <div style="padding:5px;min-width:120px">
            <div>
              <span style="display:inline-block;width:10px;height:10px;background:${COLORS.mttr_minutes};border-radius:50%"></span>
              ${t("common.compareList.mttr_minutes")}: ${mttr_minutes?.data} 分钟
            </div>
            <div>
              <span style="display:inline-block;width:10px;height:10px;background:${COLORS.mtbf_minutes};border-radius:50%"></span>
              ${t("common.compareList.mtbf_minutes")}: ${mtbf_minutes?.data} 分钟
            </div>
            <div>
              <span style="display:inline-block;width:10px;height:10px;background:${COLORS.total_fault_time_minutes};border-radius:50%"></span>
              ${t("common.compareList.total_fault_time_minutes")}: ${total_fault_time_minutes?.data} 分钟
            </div>
            <div>
              <span style="display:inline-block;width:10px;height:10px;background:${COLORS.fault_count};border-radius:50%"></span>
              ${t("common.compareList.fault_count")}: ${fault_count?.data} 次
            </div>
            <div>
              <span style="display:inline-block;width:10px;height:10px;background:${COLORS.failure_rate};border-radius:50%"></span>
              ${t("common.compareList.failure_rate")}: ${failure_rate?.data} %
            </div>
          </div>
        `;
    }
  },
  legend: {
    data: [
      t("common.compareList.mttr_minutes"), //"MTTR",
      t("common.compareList.mtbf_minutes"), // "MTBF",
      t("common.compareList.total_fault_time_minutes"), //"总故障时间",
      t("common.compareList.fault_count"), // "故障次数",
      t("common.compareList.failure_rate") //// "故障率"
    ],
    top: 12,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    // type: "category",
    // data: [],
    // axisLabel: {
    //   color: COLORS.font,
    //   interval: 0,
    //   rotate: 45
    // }
    type: "category",
    data: [],
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45,
      formatter: (value: string) => {
        if (value.includes("-")) {
          return value.split(" - ").join("\n");
        }
        return value;
      }
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.time"), //"时间 (分钟)",
      min: 0,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}`
      },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    },
    {
      type: "value",
      name: t("common.compareList.times"), //"次数",
      min: 0,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}`
      },
      splitLine: { lineStyle: { color: COLORS.splitLine } },
      position: "right"
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: []
});

// 转换数据函数
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM - DD";
      break;
    case "Year":
      formatPattern = "YYYY - MM";
      break;
    default:
      formatPattern = "HH:00";
  }

  // 遍历响应数据，按机台分组并收集所有时间点和机台ID
  responseData.forEach(item => {
    const machine = item.machine;
    let startTime;
    startTime = moment(item.start_time).format(formatPattern);

    const mttrData = item.mttr_minutes;
    const mtbfData = item.mtbf_minutes;
    const totalFaultTimeData = item.total_fault_time_minutes;
    const faultCountData = item.fault_count;
    const failureRate = item.failure_rate;

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        mttr_minutes: [],
        mtbf_minutes: [],
        total_fault_time_minutes: [],
        fault_count: [],
        failure_rate: [],
        totalMTTR: 0,
        totalMTBF: 0,
        totalTotalFaultTime: 0,
        totalFaultCount: 0,
        totalFailureRate: 0
      });
    }

    const machineData = machineMap.get(machine);
    machineData.mttr_minutes.push(mttrData);
    machineData.mtbf_minutes.push(mtbfData);
    machineData.total_fault_time_minutes.push(totalFaultTimeData);
    machineData.fault_count.push(faultCountData);
    machineData.failure_rate.push(failureRate);

    machineData.totalMTTR += mttrData;
    machineData.totalMTBF += mtbfData;
    machineData.totalTotalFaultTime += totalFaultTimeData;
    machineData.totalFaultCount += faultCountData;
    machineData.totalFailureRate += failureRate;

    timeSet.add(startTime);
    machineIdSet.add(machine);
  });

  // 对时间点进行排序
  const categories = Array.from(timeSet).sort();

  const allmachine = Array.from(machineMap.values());

  const compare = [
    {
      mttr_minutes: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.mttr_minutes,
        total: machine.totalMTTR
      })),
      mtbf_minutes: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.mtbf_minutes,
        total: machine.totalMTBF
      })),
      total_fault_time_minutes: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.total_fault_time_minutes,
        total: machine.totalTotalFaultTime
      })),
      fault_count: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.fault_count,
        total: machine.totalFaultCount
      })),
      failure_rate: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.failure_rate,
        total: machine.totalFailureRate
      }))
    }
  ];

  // 构建machines数组
  const machines = Array.from(machineIdSet).map(machine => ({
    id: machine,
    name: machine
  }));

  return {
    allmachine,
    compare,
    categories,
    machines
  };
}

// 修改数据获取函数
let currentController: AbortController | null = null;

const fetchData = async params => {
  if (!params || !params.time || params.time.length !== 2) {
    console.error("Invalid params.time");
    return {
      data: {
        categories: [],
        seriesData: [],
        isCompare: false
      }
    };
  }
  // 取消旧请求
  if (currentController) {
    currentController.abort();
  }
  currentController = new AbortController();
  const { signal } = currentController;

  const startTime = moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss.SSS");
  const endTime = moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString();
  const queryParams = {
    // Deck: params.machine || (props.machines.length > 0 ? props.machines[0].id : ""),
    StartDate: startTime,
    EndDate: endTime,
    Type: 7,
    signal
  };
  try {
    const response = await alramAnalysisApi.getListMesReportData(queryParams);
    let mode = "Hour";
    if (response.data.list.length > 0) {
      mode = response.data.list[0].type;
    }

    const data1 = transformData(response.data.list, mode);
    // machines.value = data1.machines;

    let exposedata;
    if (!params.compareMode) {
      const machine = params.machine;
      const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
      if (!machineInfo) {
        console.error(`未找到机台 ${machine} 的数据`);
        return {
          data: {
            categories: [],
            seriesData: [[], [], [], [], []],
            isCompare: false
          }
        };
      }
      const { mttr_minutes, mtbf_minutes, total_fault_time_minutes, fault_count, failure_rate } = machineInfo;
      exposedata = {
        categories: data1.categories,
        seriesData: [mttr_minutes, mtbf_minutes, total_fault_time_minutes, fault_count, failure_rate],
        isCompare: false,
        listName: ["MTTR", "MTBF", "总故障时间", "故障次数", "故障率"]
      };
      console.log(exposedata, 111111111111111);

      emits("dataReady", exposedata);
      return {
        data: {
          categories: data1.categories,
          seriesData: [mttr_minutes, mtbf_minutes, total_fault_time_minutes, fault_count, failure_rate],
          isCompare: false
        }
      };
    }

    exposedata = {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare,
      listName: ["MTTR", "MTBF", "总故障时间", "故障次数", "故障率"]
    };
    emits("dataReady", exposedata);
    return {
      data: {
        isCompare: true,
        categories: data1.categories,
        compare: data1.compare
      }
    };
  } catch (error) {
    if ((error as any).name === "CanceledError") {
      console.log("请求已取消");
    } else {
      ElMessage.error("获取故障统计数据失败");
      console.error("获取故障统计数据失败", error);
    }
    return {
      data: {
        categories: [],
        seriesData: [],
        isCompare: false
      }
    };
  }
};

// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [mttr_minutes, mtbf_minutes, total_fault_time_minutes, fault_count, failure_rate] = data.seriesData;

    chartOptions.value = {
      ...chartOptions.value,
      title: {
        ...chartOptions.value.title
      },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      },
      series: [
        {
          name: t("common.compareList.mttr_minutes"), //"MTTR",
          type: "bar",
          yAxisIndex: 0,
          data: mttr_minutes,
          itemStyle: { color: COLORS.mttr_minutes },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("common.compareList.mtbf_minutes"), //"MTBF",
          type: "bar",
          yAxisIndex: 0,
          data: mtbf_minutes,
          itemStyle: { color: COLORS.mtbf_minutes },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("common.compareList.total_fault_time_minutes"), //"总故障时间",
          type: "bar",
          yAxisIndex: 0,
          data: total_fault_time_minutes,
          itemStyle: { color: COLORS.total_fault_time_minutes },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("common.compareList.fault_count"), //"故障次数",
          type: "line",
          yAxisIndex: 1,
          data: fault_count,
          itemStyle: { color: COLORS.fault_count },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("common.compareList.failure_rate"), //"故障率",
          type: "line",
          yAxisIndex: 1,
          data: failure_rate,
          itemStyle: { color: COLORS.failure_rate },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        }
      ]
    };
  }
};

const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});

onMounted(() => {
  const defaultParams = {
    time: [moment().startOf("day").toDate(), moment().endOf("day").toDate()],
    machine: "",
    compareMode: false
  };
  fetchData(defaultParams);
});
// 组件卸载时取消未完成的请求
onUnmounted(() => {
  if (currentController) {
    currentController.abort();
  }
});
</script>
