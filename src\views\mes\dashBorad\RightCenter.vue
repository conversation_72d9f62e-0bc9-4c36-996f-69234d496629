<template>
  <div class="main h-full w-full flex flex-col cursor-pointer" @mouseenter="stopTimer" @mouseleave="startTimer">
    <div class="m-[10px] flex items-center" @click.stop>
      <el-radio-group v-model="chartType" size="small" @change="handleChartChange">
        <el-radio-button label="normal">
          <svg-icon name="single-mode" class="mr-1" />
          {{ $t("dashboard.maintenance.statisticsMode") }}
        </el-radio-button>
        <el-radio-button label="date">
          <svg-icon name="stack-mode" class="mr-1" />
          {{ $t("dashboard.maintenance.calendarMode") }}
        </el-radio-button>
      </el-radio-group>
      <div class="colorshit"></div>
    </div>
    <div v-if="chartType === 'normal'" class="industrial-maintenance-stats flex-1">
      <!-- 易损件统计 -->
      <div class="stats-group" @click="router.push({ path: '/mes/device-protect/maybe-life' })">
        <h3 class="group-title">
          <i class="icon icon-warning"></i>
          {{ $t("dashboard.maintenance.wearParts") }}
        </h3>
        <div class="stats-grid">
          <div class="stat-card" @mouseenter="hoverCard('processed')">
            <div class="stat-value animate-count">{{ data[0].Processed }}</div>
            <div class="stat-label">
              <i class="icon icon-check"></i>
              {{ $t("dashboard.maintenance.processed") }}
            </div>
            <div class="gear-icon gear-1"></div>
            <div class="gear-icon gear-2"></div>
          </div>

          <div class="stat-card critical" @mouseenter="hoverCard('overdue')">
            <div class="stat-value animate-count" style="color: #ff4d4f">{{ data[0].OverdueUnprocessed }}</div>
            <div class="stat-label">
              <i class="icon icon-alert"></i>
              {{ $t("dashboard.maintenance.overdueUnprocessed") }}
            </div>
            <div class="flashing-alert"></div>
          </div>

          <div class="stat-card" @mouseenter="hoverCard('expiring')">
            <div class="stat-value animate-count" style="color: #faad14">{{ data[0].ExpiringSoon }}</div>
            <div class="stat-label">
              <i class="icon icon-clock"></i>
              {{ $t("dashboard.maintenance.expiringSoon") }}
            </div>
            <div class="stat-sub">{{ $t("dashboard.maintenance.withinDays") }}</div>
            <!-- <div class="countdown-bar"></div> -->
          </div>
        </div>
      </div>

      <!-- 维护保养统计 -->
      <div class="stats-group" @click="router.push({ path: '/mes/device-protect/daily-protect' })">
        <h3 class="group-title">
          <i class="icon icon-maintenance"></i>
          {{ $t("dashboard.maintenance.maintenance1") }}
        </h3>
        <div class="stats-grid">
          <div class="stat-card" @mouseenter="hoverCard('mProcessed')">
            <div class="stat-value animate-count">{{ data[0].ProcessedCount }}</div>
            <div class="stat-label">
              <i class="icon icon-check"></i>
              {{ $t("dashboard.maintenance.processed") }}
            </div>
            <div class="tool-icon tool-1"></div>
          </div>

          <div class="stat-card critical" @mouseenter="hoverCard('mOverdue')">
            <div class="stat-value animate-count" style="color: #ff4d4f">{{ data[0].OverdueUnprocessedCount }}</div>
            <div class="stat-label">
              <i class="icon icon-alert"></i>
              {{ $t("dashboard.maintenance.overdueUnprocessed") }}
            </div>
            <div class="flashing-alert"></div>
          </div>

          <div class="stat-card" @mouseenter="hoverCard('daily')">
            <div class="stat-value animate-count" style="color: #faad14">{{ data[0].DailyRequiredMaintenance }}</div>
            <div class="stat-label">
              <i class="icon icon-calendar"></i>
              {{ $t("dashboard.maintenance.dailyRequired") }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="calendar-custom">
      <CalendarCustom :header-title="title" :date-data="processedDateData"></CalendarCustom>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";

const router = useRouter();
const { t } = useI18n();

const title = computed(() => t("dashboard.maintenance.title"));

// 定义 calendarData props
interface CalendarDataItem {
  Control: string;
  type: string;
  time: string;
  value: number;
}

const props = withDefaults(
  defineProps<{
    data: [
      {
        Control: string;
        Processed: number;
        OverdueUnprocessed: number;
        ExpiringSoon: number;
        ProcessedCount: number;
        OverdueUnprocessedCount: number;
        DailyRequiredMaintenance: number;
      }
    ];
    calendarData?: CalendarDataItem[];
  }>(),
  {
    data: () => [
      {
        Control: "",
        Processed: 0,
        OverdueUnprocessed: 0,
        ExpiringSoon: 0,
        ProcessedCount: 0,
        OverdueUnprocessedCount: 0,
        DailyRequiredMaintenance: 0
      }
    ],
    calendarData: () => []
  }
);

// 类型映射和颜色配置
const typeMapping = {
  维修: { key: "repair", color: "#FF5252" },
  保养: { key: "maintenance", color: "#2196F3" },
  检查: { key: "inspection", color: "#4CAF50" },
  测量: { key: "measurement", color: "#FF9800" }
};

// 数据转换函数：将API数据转换为日历组件需要的格式
const processedDateData = computed(() => {
  const result: Record<
    string,
    Array<{
      type: string;
      value: number;
      name: string;
      color: string;
    }>
  > = {};

  if (!props.calendarData || props.calendarData.length === 0) {
    return result;
  }

  props.calendarData.forEach(item => {
    // 提取日期部分 (YYYY-MM-DD)
    const dateKey = item.time.split(" ")[0];
    const typeConfig = typeMapping[item.type as keyof typeof typeMapping];

    if (!typeConfig) return;

    if (!result[dateKey]) {
      result[dateKey] = [];
    }

    // 查找是否已存在相同类型的数据
    const existingItem = result[dateKey].find((d: any) => d.type === typeConfig.key);

    if (existingItem) {
      // 如果存在，累加数值
      existingItem.value += item.value;
    } else {
      // 如果不存在，添加新项
      result[dateKey].push({
        type: typeConfig.key,
        value: item.value,
        name: t(`dashboard.maintenance.types.${typeConfig.key}`),
        color: typeConfig.color
      });
    }
  });

  return result;
});

const handleChartChange = () => {
  // isTransitioning.value = true;
  // // 切换时重新生成数据
  // singleData.value = generateSingleData();
  // stackData.value = generateStackData();
  // setTimeout(() => {
  //   isTransitioning.value = false;
  // }, 500);
};

const chartType = ref<"date" | "normal">("normal");

const hoverCard = (type: string) => {
  console.log("Hover card:", type);
  // 这里可以添加更复杂的交互逻辑
};

// 数字滚动动画
const startCountAnimation = () => {
  const counters = document.querySelectorAll(".animate-count");
  counters.forEach(counter => {
    const target = +counter.getAttribute("data-target")!;
    const duration = 1500;
    const start = 0;
    const increment = target / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        clearInterval(timer);
        current = target;
      }
      counter.textContent = Math.floor(current).toString();
    }, 16);
  });
};

// onMounted(() => {
//   startCountAnimation();
// });
let timer: number | null = null;
const toggleChartType = () => {
  chartType.value = chartType.value === "date" ? "normal" : "date";
};

const startTimer = () => {
  timer = window.setInterval(toggleChartType, 5000);
};

const stopTimer = () => {
  if (timer !== null) {
    clearInterval(timer);
    timer = null;
  }
};

onMounted(() => {
  startCountAnimation();
  startTimer();
});

// 组件卸载时清除定时器（避免内存泄漏）
onUnmounted(() => {
  stopTimer();
});
</script>

<style scoped>
.main {
  background: rgb(89 98 123 / 23%);
  :deep(.el-radio-button__inner) {
    color: #a1a1a1;
    background: rgb(255 255 255 / 10%);
    border: 1px solid rgb(255 255 255 / 20%);
    transition: all 0.3s;
    &:hover {
      color: #ffffff;
      background: rgb(255 255 255 / 20%);
    }
  }
  :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
    color: #ffffff;
    background: linear-gradient(90deg, #3b7bff, #00d1ff);
    border-color: transparent;
    box-shadow: 0 0 10px rgb(59 123 255 / 50%);
  }
}
.industrial-maintenance-stats {
  position: relative;
  display: grid;
  gap: 20px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 30%);
}
.group-title {
  display: flex;
  align-items: center;
  margin: 0 0 12px;
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5px;
}
.group-title .icon {
  margin-right: 8px;
  filter: drop-shadow(0 0 3px currentColor);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}
.stat-card {
  position: relative;
  padding: 16px;
  overflow: hidden;
  text-align: center;
  background: rgb(24 58 91 / 50%);
  border: 1px solid rgb(64 158 255 / 10%);
  border-radius: 8px;
  transition: all 0.3s ease;
}
.stat-card:hover {
  background: rgb(24 58 91 / 70%);
  border-color: rgb(64 158 255 / 30%);
  box-shadow: 0 6px 16px rgb(0 0 0 / 20%);
  transform: translateY(-3px);
}
.stat-card.critical {
  background: rgb(255 77 79 / 15%);
  border-color: rgb(255 77 79 / 20%);
  animation: criticalPulse 3s infinite;
}
.stat-card.critical:hover {
  background: rgb(255 77 79 / 25%);
}
.stat-card.critical::after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  content: "";
  background: linear-gradient(90deg, transparent, #ff4d4f, transparent);
}
.stat-value {
  margin-bottom: 6px;
  font-family: Arial, sans-serif;
  font-size: 50px;
  font-weight: 700;
  color: #4fc08d;
  text-shadow: 0 0 5px rgb(64 158 255 / 50%);
}
.stat-card.critical .stat-value {
  color: #ffcccc;
  text-shadow: 0 0 5px rgb(255 77 79 / 50%);
}
.stat-label {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
  font-size: 16px;
  color: rgb(255 255 255 / 90%);
}
.stat-label .icon {
  margin-right: 6px;
}
.stat-sub {
  font-size: 11px;
  color: rgb(255 255 255 / 60%);
}

/* 图标样式 */
.icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.icon-warning {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFA940"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>');
}
.icon-check {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2352C41A"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/></svg>');
}
.icon-alert {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4D4F"><path d="M12 2L1 21h22L12 2zm0 3.5L18.5 19h-13L12 5.5z"/><circle cx="12" cy="16.5" r="1"/><path d="M12 10v4"/></svg>');
}
.icon-clock {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FAAD14"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/><path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>');
}
.icon-maintenance {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%231890FF"><path d="M11.5 12.5H13V15h1.5v-2.5H17v-1.5h-2.5V9h-1.5v2.5h-2zm-5 0H8V15h1.5v-2.5H12v-1.5H9.5V9H8v2.5H6v1.5h.5z"/><path d="M21 12c0-4.96-4.04-9-9-9s-9 4.04-9 9 4.04 9 9 9 9-4.04 9-9zm-9 7.5c-4.69 0-8.5-3.81-8.5-8.5S7.31 3.5 12 3.5s8.5 3.81 8.5 8.5-3.81 8.5-8.5 8.5z"/></svg>');
}
.icon-calendar {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2372C140"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11z"/><path d="M7 11h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2z"/></svg>');
}

/* 动画元素 */
.gear-icon {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23409EFF"><path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"/></svg>');
  opacity: 0.2;
  animation: gearRotate 6s linear infinite;
}
.gear-1 {
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
}
.gear-2 {
  bottom: 15px;
  left: 15px;
  width: 18px;
  height: 18px;
  animation-duration: 8s;
  animation-direction: reverse;
}
.tool-icon {
  position: absolute;
  width: 24px;
  height: 24px;
  opacity: 0.15;
}
.tool-1 {
  right: 10px;
  bottom: 10px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23409EFF"><path d="M21.71 11.29l-9-9c-.39-.39-1.02-.39-1.41 0l-9 9c-.39.39-.39 1.02 0 1.41l9 9c.39.39 1.02.39 1.41 0l9-9c.39-.38.39-1.01 0-1.41zM14 14.5V12h-4v2H8v-2.5l4-4 4 4z"/></svg>');
}
.pulsing-alert {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  pointer-events: none;
  background: rgb(255 77 79 / 20%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s ease-out infinite;
}
.flashing-alert {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 8px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
  box-shadow: 0 0 5px #ff4d4f;
  animation: flash 1.5s steps(2) infinite;
}
.countdown-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #faad14, transparent);
  animation: countdown 15s linear forwards;
}
.progress-dots {
  position: absolute;
  bottom: 10px;
  left: 50%;
  display: flex;
  gap: 4px;
  transform: translateX(-50%);
}
.progress-dots::before {
  width: 6px;
  height: 6px;
  content: "";
  background: #52c41a;
  border-radius: 50%;
  animation: dotPulse 1.5s infinite;
}
.progress-dots::after {
  width: 6px;
  height: 6px;
  content: "";
  background: rgb(82 196 26 / 50%);
  border-radius: 50%;
  animation: dotPulse 1.5s infinite 0.5s;
}

/* 动画定义 */
@keyframes gearRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(0.8);
  }
  70% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1.3);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@keyframes flash {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes countdown {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

@keyframes dotPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes criticalPulse {
  0% {
    box-shadow: 0 0 0 0 rgb(255 77 79 / 40%);
  }
  70% {
    box-shadow: 0 0 0 10px rgb(255 77 79 / 0%);
  }
  100% {
    box-shadow: 0 0 0 0 rgb(255 77 79 / 0%);
  }
}
</style>
